import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/mixins/locale_aware_cubit.dart';
import 'package:gp_stock_app/features/profile/logic/auth_aware_cubit.dart';

/// Example showing how to use LocaleAwareMixin
class ExampleState {
  final String currentLocale;
  final bool isLoading;

  const ExampleState({
    this.currentLocale = 'en-US',
    this.isLoading = false,
  });

  ExampleState copyWith({
    String? currentLocale,
    bool? isLoading,
  }) {
    return ExampleState(
      currentLocale: currentLocale ?? this.currentLocale,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

/// Example 1: Using LocaleAwareMixin
class ExampleLocaleAwareCubit extends Cubit<ExampleState> with LocaleAwareMixin<ExampleState> {
  ExampleLocaleAwareCubit() : super(const ExampleState()) {
    initLocaleAware(); // Initialize locale awareness
  }

  @override
  void onLocaleChanged(String locale) {
    // Handle locale change
    emit(state.copyWith(currentLocale: locale));
    print('Locale changed to: $locale');
    // You can add more locale-specific logic here
  }

  @override
  Future<void> close() {
    // Make sure to call super.close() to properly dispose resources
    return super.close();
  }
}

/// Example 2: Using LocaleAwareCubit (inheritance approach)
class ExampleLocaleAwareCubit2 extends LocaleAwareCubit<ExampleState> {
  ExampleLocaleAwareCubit2() : super(const ExampleState());

  @override
  void onLocaleChanged(String locale) {
    emit(state.copyWith(currentLocale: locale));
    print('Locale changed to: $locale');
  }
}

/// Example 3: Combining with existing AuthAwareCubit
/// For cubits that already extend AuthAwareCubit and want locale awareness too
class ExampleAuthAndLocaleAwareCubit extends AuthAwareCubit<ExampleState> with LocaleAwareMixin<ExampleState> {
  ExampleAuthAndLocaleAwareCubit() : super(const ExampleState()) {
    initLocaleAware(); // Initialize locale awareness in addition to auth awareness
  }

  @override
  void onLoggedIn() {
    // Handle login
    print('User logged in');
  }

  @override
  void onLoggedOut() {
    // Handle logout
    print('User logged out');
  }

  @override
  void onLocaleChanged(String locale) {
    // Handle locale change
    emit(state.copyWith(currentLocale: locale));
    print('Locale changed to: $locale');
  }

  @override
  Future<void> close() {
    // Important: Call super.close() to dispose both auth and locale subscriptions
    return super.close();
  }
}

/// Example 4: Using AuthAndLocaleAwareMixin for new cubits that need both
class ExampleCombinedCubit extends Cubit<ExampleState> with AuthAndLocaleAwareMixin<ExampleState> {
  ExampleCombinedCubit() : super(const ExampleState()) {
    initAuthAndLocaleAware(); // Initialize both auth and locale awareness
  }

  @override
  void onLoggedIn() {
    print('User logged in');
  }

  @override
  void onLoggedOut() {
    print('User logged out');
  }

  @override
  void onLocaleChanged(String locale) {
    emit(state.copyWith(currentLocale: locale));
    print('Locale changed to: $locale');
  }

  @override
  Future<void> close() {
    // The mixin handles disposing both subscriptions
    return super.close();
  }
}
