import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';

/// Mixin for cubits that need to be aware of locale changes
/// Similar to AuthAwareCubit but for locale changes
/// 
/// Usage example:
/// ```dart
/// class MyCubit extends Cubit<MyState> with LocaleAwareMixin<MyState> {
///   MyCubit() : super(MyInitialState()) {
///     initLocaleAware();
///   }
/// 
///   @override
///   void onLocaleChanged(String locale) {
///     // Handle locale change
///     print('Locale changed to: $locale');
///   }
/// }
/// ```
mixin LocaleAwareMixin<T extends Object> on Cubit<T> {
  late StreamSubscription<String> _localeSubscription;

  /// Initialize the locale awareness
  /// Call this in the cubit's constructor
  void initLocaleAware() {
    _localeSubscription = getIt<UserCubit>().currentLocaleController.stream.listen((String locale) {
      onLocaleChanged(locale);
    });
  }

  /// Called when locale changes
  /// Override this method to handle locale changes
  void onLocaleChanged(String locale);

  /// Override the close method to cancel the subscription
  /// Make sure to call super.close() in your cubit's close method
  @override
  Future<void> close() {
    _localeSubscription.cancel();
    return super.close();
  }
}

/// Abstract class version for cubits that prefer inheritance over mixin
/// Compatible with existing AuthAwareCubit pattern
abstract class LocaleAwareCubit<T extends Object> extends Cubit<T> {
  late StreamSubscription<String> _localeSubscription;

  LocaleAwareCubit(super.initialState) {
    _localeSubscription = getIt<UserCubit>().currentLocaleController.stream.listen((String locale) {
      onLocaleChanged(locale);
    });
  }

  /// Called when locale changes
  void onLocaleChanged(String locale);

  @override
  Future<void> close() {
    _localeSubscription.cancel();
    return super.close();
  }
}

/// Combined mixin for cubits that need both auth and locale awareness
/// This allows existing AuthAware cubits to also be locale aware
mixin AuthAndLocaleAwareMixin<T extends Object> on Cubit<T> {
  late StreamSubscription<bool> _loginSubscription;
  late StreamSubscription<String> _localeSubscription;

  /// Initialize both auth and locale awareness
  /// Call this in the cubit's constructor
  void initAuthAndLocaleAware() {
    _loginSubscription = getIt<UserCubit>().loginStatusStream.listen((bool isLogin) {
      if (isLogin) {
        onLoggedIn();
      } else {
        onLoggedOut();
      }
    });

    _localeSubscription = getIt<UserCubit>().currentLocaleController.stream.listen((String locale) {
      onLocaleChanged(locale);
    });
  }

  /// Called when user logs in
  void onLoggedIn();

  /// Called when user logs out
  void onLoggedOut();

  /// Called when locale changes
  void onLocaleChanged(String locale);

  @override
  Future<void> close() {
    _loginSubscription.cancel();
    _localeSubscription.cancel();
    return super.close();
  }
}
