import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/services/user/user_cubit.dart';

abstract class AuthAwareCubit<T extends Object> extends Cubit<T> {
  late StreamSubscription<bool> _loginSubscription;

  AuthAwareCubit(super.initialState) {
    _loginSubscription = getIt<UserCubit>().loginStatusStream.listen((bool isLogin) {
      if (isLogin) {
        onLoggedIn();
      } else {
        onLoggedOut();
      }
    });
  }

  void onLoggedIn();

  void onLoggedOut();

  @override
  Future<void> close() {
    _loginSubscription.cancel();
    return super.close();
  }
}
