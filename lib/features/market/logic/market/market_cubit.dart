import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:gp_stock_app/core/dependency_injection/injectable.dart';
import 'package:gp_stock_app/core/mixins/locale_aware_cubit.dart';
import 'package:gp_stock_app/core/utils/extension/future_list_extensions.dart';
import 'package:gp_stock_app/features/market/logic/market_status/market_status_cubit.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:injectable/injectable.dart';

import '../../../../shared/models/stock/stock_response.dart';
import '../../domain/models/component_stock_response.dart';
import '../../domain/models/depth_quote_model.dart';
import '../../domain/models/dist_response.dart';
import '../../domain/models/plate_info_request/plate_info_request.dart';
import '../../domain/models/plate_info_response.dart/plate_info_response.dart';
import '../../domain/models/plate_response.dart';
import '../../domain/models/stock_kline_data.dart';
import '../../domain/models/stock_table_response.dart';
import '../../domain/repositories/market_repository.dart';
import '../../utils/utils.dart';

part 'market_state.dart';

@injectable
class MarketCubit extends LocaleAwareCubit<MarketState> {
  MarketCubit(this.marketRepo) : super(const MarketState());
  Timer? _pollTimer;
  final MarketRepository marketRepo;

  @override
  void onLocaleChanged(String locale) {
    fetchTableData(isHome: true);
  }

  void init({bool isRefresh = false}) {
    _startPolling();
    if (isRefresh) {
      emit(state.copyWith(
        plateFetchStatusA: DataStatus.idle,
        plateResponseA: null,
        stockFetchStatus: DataStatus.idle,
        stockResponse: null,
        distFetchStatus: DataStatus.idle,
        distResponse: null,
        tableData: null,
        tableFetchStatus: DataStatus.idle,
        pageNumber: 1,
      ));
    }

    fetchPlateList();
    fetchTableData();
    fetchDist();
    fetchStockList();
  }

  void _startPolling() {
    _pollTimer?.cancel();
    _pollTimer = Timer.periodic(const Duration(milliseconds: 3500), (_) => pollMarket());
  }

  @override
  Future<void> close() {
    _pollTimer?.cancel();
    return super.close();
  }

  void pollMarket() {
    final currentMarket = state.selectedTodaysTab.market;
    final isCurrentMarketOpen = getIt<MarketStatusCubit>().isMarketOpen(currentMarket);
    if (isCurrentMarketOpen) {
      fetchPlateList(isPolling: true);
      fetchStockList();
      fetchDist();
      updateTableTab(state.selectedTableTab, isLive: true);
    }
  }

  void fetchPlateList({
    bool isLoadMore = false,
    String? field,
    String? order,
    bool isPolling = false,
  }) async {
    if (state.hasReachedMax ||
        state.plateFetchStatusA == DataStatus.loading ||
        state.paginationStatus == DataStatus.loading) {
      return;
    }
    if (!isLoadMore && !isPolling) {
      emit(state.copyWith(plateFetchStatusA: DataStatus.loading, pageNumber: 1, hasReachedMax: false));
    } else {
      emit(state.copyWith(paginationStatus: DataStatus.loading));
    }
    int page = isLoadMore ? state.pageNumber + 1 : state.pageNumber;
    try {
      final result =
          await marketRepo.fetchPlateList(tab: state.selectedTodaysTab, pageNum: page, field: field, order: order);

      if (isLoadMore) {
        emit(
          state.copyWith(
            plateFetchStatusA: DataStatus.success,
            plateResponseA: PlateResponse(
              code: result.data?.code ?? 0,
              data: PlateData(
                records: [
                  ...?state.plateResponseA?.data?.records,
                  ...?result.data?.data?.records,
                ],
              ),
              msg: result.data?.msg ?? '',
            ),
            pageNumber: page,
            paginationStatus: DataStatus.success,
            hasReachedMax: result.data?.data?.records?.isEmpty ?? true,
          ),
        );
      } else {
        emit(
          state.copyWith(
            plateFetchStatusA: DataStatus.success,
            plateResponseA: result.data,
            pageNumber: page,
            paginationStatus: DataStatus.success,
            hasReachedMax: result.data?.data?.records?.isEmpty ?? true,
          ),
        );
      }
    } catch (e) {
      emit(state.copyWith(plateFetchStatusA: DataStatus.failed));
    }
  }

  Future<DistResponse> fetchDist() async {
    emit(state.copyWith(distFetchStatus: DataStatus.loading));
    try {
      final result = await marketRepo.fetchDist(type: state.selectedTodaysTab);
      emit(
        state.copyWith(
          distFetchStatus: DataStatus.success,
          distResponse: result.data,
        ),
      );
      return result.data ?? const DistResponse();
    } catch (e) {
      emit(state.copyWith(distFetchStatus: DataStatus.failed));
      rethrow;
    }
  }

  Future<StockResponse> fetchStockList() async {
    emit(state.copyWith(stockFetchStatus: DataStatus.loading));
    try {
      final result = await marketRepo.fetchStockList();

      final stockList = result.data?.data?.list;
      if (stockList != null) {
        for (final stock in stockList) {
          if (stock.market != null && stock.securityType != null && stock.symbol != null) {
            // fetchMiniKline(stock);
          }
        }
      }

      emit(
        state.copyWith(
          stockFetchStatus: DataStatus.success,
          stockResponse: result.data,
        ),
      );
      return result.data ?? const StockResponse();
    } catch (e) {
      emit(state.copyWith(stockFetchStatus: DataStatus.failed));

      rethrow;
    }
  }

  void changePeriodKline({StockItem? stock, String? period}) {
    emit(state.copyWith(selectedPeriod: period));
    fetchKlineData(stock: stock);
  }

  void fetchKlineData({StockItem? stock}) async {
    if (stock == null) return;
    emit(state.copyWith(klineFetchStatus: DataStatus.loading));
    try {
      final result = await marketRepo.fetchKlineData(stock, state.selectedPeriod);
      final updatedList = updateKlineList(
        result: result.data,
        coreKlineList: state.coreKlineList,
      );

      emit(
        state.copyWith(
          klineFetchStatus: DataStatus.success,
          coreKlineList: updatedList,
        ),
      );
    } catch (e) {
      emit(state.copyWith(klineFetchStatus: DataStatus.failed));
    }
  }

  void fetchDepthQuote({StockItem? stock}) async {
    if (stock == null) return;
    emit(state.copyWith(depthQuoteFetchStatus: DataStatus.loading));
    try {
      final result = await marketRepo.fetchDepthQuote(stock);

      emit(
        state.copyWith(
          depthQuoteFetchStatus: DataStatus.success,
          depthQuoteResponse: result.data,
        ),
      );
    } catch (e) {
      emit(state.copyWith(depthQuoteFetchStatus: DataStatus.failed));
    }
  }

  Future fetchTableData({
    bool isLoadMore = false,
    bool isHome = false,
    bool isLive = false,
    int pageSize = 20,
    bool skipLoading = false,
  }) async {
    if (!isLive && !skipLoading) emit(state.copyWith(tableFetchStatus: DataStatus.loading));
    if (isLoadMore) emit(state.copyWith(isPaginating: true));
    try {
      // Following H5 app approach: increase pageSize instead of page number for load more
      // Always use page 1 and adjust pageSize based on current data length (API not providing page number details)
      final int adjustedPageSize;

      if (isLive) {
        // For polling/live updates, use the current pageSize from state
        adjustedPageSize = state.currentPageSize;
      } else if (isLoadMore) {
        // For load more, increase pageSize by adding more items
        adjustedPageSize = state.currentPageSize + pageSize;
      } else {
        // For initial load, use the provided pageSize
        adjustedPageSize = pageSize;
      }

      final result = await marketRepo.fetchTableData(
        marketTableType: state.selectedMarketTableType ?? _getDefaultMarketTypeForTab(state.selectedTodaysTab),
        todaysTab: state.selectedTodaysTab,
        sortType: state.sortType,
        order: state.order,
        pageNum: 1, // Always use page 1
        isHome: isHome,
        pageSize: adjustedPageSize,
      );

      emit(
        state.copyWith(
          tableFetchStatus: DataStatus.success,
          tableData: result.data,
          isPaginating: false,
          currentPageSize: adjustedPageSize, // Update the current pageSize in state
        ),
      );
      if (isLoadMore) {
        emit(
          state.copyWith(
            tableData: StockTableResponse(
              code: result.data?.code ?? 0,
              data: StockTableData(
                list: state.tableData?.data?.list.mergeDedupKeepFirst(result.data?.data?.list, (item) => item.name!),
                pageNum: result.data?.data?.pageNum ?? 0,
                pageSize: result.data?.data?.pageSize ?? 0,
                totalNum: result.data?.data?.totalNum ?? 0,
              ),
              msg: result.data?.msg ?? '',
            ),
            tableFetchStatus: DataStatus.success,
            isPaginating: false,
          ),
        );
      } else {
        emit(
          state.copyWith(
            tableFetchStatus: DataStatus.success,
            tableData: result.data,
            isPaginating: false,
          ),
        );
      }
    } on Exception {
      emit(state.copyWith(tableFetchStatus: DataStatus.failed));
    }
  }

  void updateTableTab(
    int tab, {
    bool isLive = false,
  }) {
    // Only reset pageSize when it's not a live/polling call
    if (isLive) {
      emit(state.copyWith(selectedTableTab: tab)); // Don't reset pageSize for polling
    } else {
      emit(state.copyWith(selectedTableTab: tab, currentPageSize: 20)); // Reset pageSize when switching tabs
    }
    fetchTableData(isLive: isLive);
  }

  (int, String) getTableSortTypeAndOrder() => switch (state.selectedTableTab) {
        _ => (0, 'DESC'),
      };

  void updateTodaysTab(TodaysTab tab, {bool isHome = false, bool skipLoading = false}) {
    if (tab == state.selectedTodaysTab) return;
    emit(
      state.copyWith(
        selectedTodaysTab: tab,
        selectedMarketTableType: isHome ? null : _getDefaultMarketTypeForTab(tab),
        resetSelectedMarketTableType: isHome,
        currentPageSize: 20, // Reset pageSize when switching tabs
      ),
    );
    fetchDist();
    fetchPlateList();
    fetchTableData(isHome: isHome, skipLoading: skipLoading);
  }

  MarketType _getDefaultMarketTypeForTab(TodaysTab tab) => switch (tab) {
        TodaysTab.aShares => MarketType.shenzhen,
        TodaysTab.hkShares => MarketType.main,
        TodaysTab.usShares => MarketType.star,
      };

  void updateMarketTableType(MarketType type) {
    emit(state.copyWith(
        selectedMarketTableType: type, currentPageSize: 20)); // Reset pageSize when switching market type
    fetchTableData();
  }

  void updateMainHeaderTab(int index) => emit(state.copyWith(mainHeaderIndex: index));

  void handleSortByChange() {
    final nextSortType = state.sortByChangeAsc == null
        ? SortType.ASC
        : state.sortByChangeAsc == SortType.ASC
            ? SortType.DESC
            : null;

    emit(state.copyWith(
      resetSortByPrice: true,
      sortByChangeAsc: nextSortType,
      resetSortByChange: nextSortType == null,
      sortType: nextSortType == null ? 0 : 2,
      order: nextSortType == null ? 'DESC' : (nextSortType == SortType.ASC ? 'ASC' : 'DESC'),
    ));

    fetchTableData();
  }

  void handleSortByChangePlateList() {
    final nextSortType = state.sortByChangePlateListAsc == null
        ? SortType.ASC
        : state.sortByChangePlateListAsc == SortType.ASC
            ? SortType.DESC
            : null;

    emit(state.copyWith(
      sortByChangePlateListAsc: nextSortType,
      resetSortByChangePlateList: nextSortType == null,
    ));

    fetchPlateList(
      field: 'gain',
      order: nextSortType == null ? 'DESC' : (nextSortType == SortType.ASC ? 'ASC' : 'DESC'),
    );
  }

  void handleSortByPrice() {
    final nextSortType = state.sortByPriceAsc == null
        ? SortType.ASC
        : state.sortByPriceAsc == SortType.ASC
            ? SortType.DESC
            : null;

    emit(state.copyWith(
      resetSortByChange: true,
      sortByPriceAsc: nextSortType,
      resetSortByPrice: nextSortType == null,
      sortType: nextSortType == null ? 0 : 1,
      order: nextSortType == null ? 'DESC' : (nextSortType == SortType.ASC ? 'ASC' : 'DESC'),
    ));

    fetchTableData();
  }

  void fetchPlateInfo({required PlateInfoRequest request, bool isLoadMore = false}) async {
    if (!isLoadMore) {
      emit(state.copyWith(plateInfoFetchStatus: DataStatus.loading, pageNumber: 1, hasReachedMax: false));
    } else {
      if (state.hasReachedMax ||
          state.plateInfoFetchStatus == DataStatus.loading ||
          state.paginationStatus == DataStatus.loading) {
        return;
      }

      emit(state.copyWith(paginationStatus: DataStatus.loading));
    }
    try {
      int page = isLoadMore ? state.pageNumber + 1 : state.pageNumber;

      final result = await marketRepo.getPlateInfo(request.copyWith(page: page));
      if (result.data != null) {
        if (isLoadMore) {
          emit(state.copyWith(
            plateInfoFetchStatus: DataStatus.success,
            plateInfoResponse: PlateInfoResponse(
              data: [
                ...?state.plateInfoResponse?.data,
                ...?result.data?.data,
              ],
              total: result.data?.total ?? 0,
            ),
            pageNumber: page,
            paginationStatus: DataStatus.success,
            hasReachedMax: (state.plateInfoResponse?.data.length ?? 0) >= (result.data?.total ?? 0),
          ));
        } else {
          emit(state.copyWith(
            plateInfoFetchStatus: DataStatus.success,
            plateInfoResponse: result.data,
            pageNumber: page,
            paginationStatus: DataStatus.success,
            hasReachedMax: (result.data?.data.length ?? 0) >= (result.data?.total ?? 0),
          ));
        }
      } else {
        emit(state.copyWith(
          plateInfoFetchStatus: DataStatus.failed,
        ));
      }
    } on Exception {
      emit(state.copyWith(
        plateInfoFetchStatus: DataStatus.failed,
        paginationStatus: DataStatus.failed,
      ));
    }
  }

  void handleSortByChangePlateInfo({required PlateInfoRequest request}) {
    final nextSortType = state.sortByChangePlateInfoAsc == null
        ? SortType.ASC
        : state.sortByChangePlateInfoAsc == SortType.ASC
            ? SortType.DESC
            : null;

    emit(state.copyWith(
      resetSortByPricePlateInfo: true,
      sortByChangePlateInfoAsc: nextSortType,
      resetSortByChangePlateInfo: nextSortType == null,
    ));

    fetchPlateInfo(
        request:
            request.copyWith(order: nextSortType == null ? 'DESC' : (nextSortType == SortType.ASC ? 'ASC' : 'DESC')));
  }

  void handleSortByPricePlateInfo({required PlateInfoRequest request}) {
    final nextSortType = state.sortByPricePlateInfoAsc == null
        ? SortType.ASC
        : state.sortByPricePlateInfoAsc == SortType.ASC
            ? SortType.DESC
            : null;

    emit(state.copyWith(
      resetSortByChangePlateInfo: true,
      sortByPricePlateInfoAsc: nextSortType,
      resetSortByPricePlateInfo: nextSortType == null,
    ));

    fetchPlateInfo(
        request:
            request.copyWith(order: nextSortType == null ? 'DESC' : (nextSortType == SortType.ASC ? 'ASC' : 'DESC')));
  }

  void resetPlateData() => emit(state.copyWith(
        pageNumber: 0,
        hasReachedMax: false,
      ));
}
